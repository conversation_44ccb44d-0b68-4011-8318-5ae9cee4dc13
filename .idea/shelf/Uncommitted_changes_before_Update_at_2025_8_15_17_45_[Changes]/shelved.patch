Index: src/components/MCP/BaseMCPCard/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport {Flex, Divider, Typography, Tooltip} from 'antd';\nimport {memo, MouseEvent, useMemo, ReactNode} from 'react';\nimport {Button} from '@panda-design/components';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport UpdateInfo from '@/components/MCP/UpdateInfo';\nimport SvgEye from '@/icons/mcp/Eye';\nimport SvgCallCount from '@/icons/mcp/CallCount';\nimport SvgVip from '@/icons/mcp/Vip';\nimport {\n    actionButtonStyle,\n    cardContentStyle,\n    containerCss,\n    DescriptionContainer,\n    DescriptionText,\n    dividerStyle,\n    EllipsisOverlay,\n    formatCount,\n    fullWidthButtonStyle,\n    hoverActionsStyle,\n    iconStyle,\n    protocolTextStyle,\n    statsContainerStyle,\n} from './styles';\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n    showDepartment?: boolean;\n    workspaceId?: number;\n    onCardClick: () => void;\n    onViewCountClick: (e: MouseEvent) => void;\n    onPlaygroundClick?: (e: MouseEvent) => void;\n    renderActions?: () => ReactNode;\n    infoType?: 'publish' | 'update';\n    showUpdateInfo?: boolean;\n}\n\nconst BaseMCPCard = ({\n    server,\n    refresh,\n    workspaceId,\n    onCardClick,\n    onViewCountClick,\n    onPlaygroundClick,\n    renderActions,\n    showUpdateInfo = false,\n}: Props) => {\n    const tags = useMemo(\n        () => (server.labels ?? []).map((label, index) => ({\n            id: label.id || index,\n            label: label.labelValue,\n        })),\n        [server.labels]\n    );\n\n    return (\n        <MCPCard vertical onClick={onCardClick} className={containerCss}>\n            <Flex gap={14} align=\"center\">\n                <div style={{position: 'relative', display: 'inline-block'}}>\n                    <MCPServerAvatar\n                        icon={server.icon}\n                        style={server.official ? {\n                            border: '2px solid',\n                            borderImageSource:\n                                'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',\n                            borderImageSlice: 1,\n                        } : undefined}\n                    />\n                    {server.official && (\n                        <SvgVip\n                            style={{\n                                position: 'absolute',\n                                bottom: -7,\n                                right: -4,\n                                fontSize: '23px',\n                            }}\n                        />\n                    )}\n                </div>\n                <Flex vertical justify=\"space-between\" style={cardContentStyle} gap={4}>\n                    <Typography.Title level={4} ellipsis>\n                        {server.name}\n                    </Typography.Title>\n                    <Flex align=\"center\" gap={4}>\n                        <Typography.Text style={protocolTextStyle}>\n                            {getServerTypeText(server.serverProtocolType)}\n                        </Typography.Text>\n                        <Divider type=\"vertical\" style={{borderColor: '#D9D9D9'}} />\n                        <Typography.Text style={protocolTextStyle}>\n                            {server.serverProtocolType}\n                        </Typography.Text>\n                    </Flex>\n                </Flex>\n            </Flex>\n            <Tooltip title={server.description || '暂无描述'} placement=\"top\">\n                <DescriptionContainer>\n                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>\n                    <EllipsisOverlay />\n                </DescriptionContainer>\n            </Tooltip>\n            <TagGroup\n                labels={tags}\n                color=\"light-purple\"\n                prefix={null}\n                style={{flexShrink: 1, overflow: 'hidden'}}\n                gap={4}\n            />\n            <Divider style={dividerStyle} />\n            <Flex justify=\"space-between\" align=\"center\">\n                <Flex align=\"center\" gap={12}>\n                    <Tooltip title=\"浏览量\">\n                        <Flex\n                            align=\"center\"\n                            gap={4}\n                            onClick={onViewCountClick}\n                            className={statsContainerStyle}\n                        >\n                            <SvgEye style={iconStyle} />\n                            {formatCount(server.serverMetrics?.viewCount || 0)}\n                        </Flex>\n                    </Tooltip>\n                    <Tooltip title=\"调用量\">\n                        <Flex\n                            align=\"center\"\n                            gap={4}\n                            className={statsContainerStyle}\n                        >\n                            <SvgCallCount style={iconStyle} />\n                            {formatCount(server.serverMetrics?.callCount || 0)}\n                        </Flex>\n                    </Tooltip>\n                </Flex>\n                {showUpdateInfo ? (\n                    <UpdateInfo\n                        username={server.lastModifyUser}\n                        time={server.lastModifyTime}\n                        variant=\"space-card\"\n                    />\n                ) : (\n                    <Flex align=\"center\">\n                        <MCPCollectButton\n                            refresh={refresh}\n                            favorite={server.favorite}\n                            serverId={server.id}\n                            showText\n                            style={actionButtonStyle}\n                        />\n                        <Divider type=\"vertical\" style={{borderColor: '#D9D9D9'}} />\n                        <MCPSubscribeButton\n                            refresh={refresh}\n                            workspaceId={workspaceId || server.workspaceId}\n                            id={server.id}\n                            showText\n                            style={actionButtonStyle}\n                        />\n                    </Flex>\n                )}\n            </Flex>\n            <Flex align=\"center\" justify=\"space-between\" gap={10} className={`hover-actions ${hoverActionsStyle}`}>\n                {renderActions ? renderActions() : (\n                    <Button type=\"primary\" onClick={onPlaygroundClick} style={fullWidthButtonStyle}>\n                        去MCP Playground使用\n                    </Button>\n                )}\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default memo(BaseMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/index.tsx b/src/components/MCP/BaseMCPCard/index.tsx
--- a/src/components/MCP/BaseMCPCard/index.tsx	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/src/components/MCP/BaseMCPCard/index.tsx	(date 1755246649950)
@@ -13,6 +13,7 @@
 import SvgEye from '@/icons/mcp/Eye';
 import SvgCallCount from '@/icons/mcp/CallCount';
 import SvgVip from '@/icons/mcp/Vip';
+import {IconArrowRight1} from '@/icons/mcp';
 import {
     actionButtonStyle,
     cardContentStyle,
@@ -61,8 +62,8 @@
     );
 
     return (
-        <MCPCard vertical onClick={onCardClick} className={containerCss}>
-            <Flex gap={14} align="center">
+        <MCPCard vertical onClick={onCardClick} className={containerCss({official: server.official})}>
+            <Flex gap={16} align="center">
                 <div style={{position: 'relative', display: 'inline-block'}}>
                     <MCPServerAvatar
                         icon={server.icon}
@@ -85,7 +86,7 @@
                     )}
                 </div>
                 <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
-                    <Typography.Title level={4} ellipsis>
+                    <Typography.Title level={4} ellipsis style={{color: '#181818'}}>
                         {server.name}
                     </Typography.Title>
                     <Flex align="center" gap={4}>
@@ -142,6 +143,7 @@
                         username={server.lastModifyUser}
                         time={server.lastModifyTime}
                         variant="space-card"
+                        showAvatar={false}
                     />
                 ) : (
                     <Flex align="center">
@@ -165,8 +167,8 @@
             </Flex>
             <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                 {renderActions ? renderActions() : (
-                    <Button type="primary" onClick={onPlaygroundClick} style={fullWidthButtonStyle}>
-                        去MCP Playground使用
+                    <Button onClick={onPlaygroundClick} style={fullWidthButtonStyle}>
+                        <IconArrowRight1 style={{fontSize: 16}} />去MCP Playground使用
                     </Button>
                 )}
             </Flex>
Index: src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts b/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
deleted file mode 100644
--- a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ /dev/null	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
@@ -1,129 +0,0 @@
-import styled from '@emotion/styled';
-import {css} from '@emotion/css';
-import {colors} from '@/constants/colors';
-
-export const containerCss = css`
-    padding: 16px 20px 12px;
-    position: relative;
-    transition: all 0.3s ease;
-    &:hover {
-        position: relative;
-        z-index: 1;
-        background: ${colors.white};
-        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
-        border-radius: 6px;
-        padding-bottom: 48px;
-        margin-bottom: -48px;
-        width: auto;
-        .hover-actions {
-            opacity: 1;
-            min-height: 32px;
-            padding: 0 20px 16px;
-        }
-    }
-`;
-
-export const hoverActionsStyle = css`
-    position: absolute;
-    bottom: 0;
-    left: 0;
-    right: 0;
-    max-height: 0;
-    opacity: 0;
-    transition: all 0.3s ease;
-`;
-
-export const DescriptionContainer = styled.div`
-    margin: 15px 0 13px;
-    padding: 10px 12px 9px;
-    background-color: ${colors['gray-3']};
-    font-size: 14px;
-    line-height: 1.4;
-    position: relative;
-    height: 57px;
-    overflow: hidden;
-`;
-
-export const DescriptionText = styled.div`
-    display: -webkit-box;
-    -webkit-line-clamp: 2;
-    -webkit-box-orient: vertical;
-    text-overflow: ellipsis;
-    word-break: break-word;
-    overflow: hidden;
-`;
-
-export const EllipsisOverlay = styled.div`
-    position: absolute;
-    bottom: 9px;
-    right: 12px;
-    padding-left: 10px;
-    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);
-    pointer-events: none;
-`;
-
-export const cardContentStyle = {
-    overflow: 'hidden',
-    flex: 1,
-};
-
-export const protocolTextStyle = {
-    color: colors['gray-7'],
-    fontSize: 12,
-    lineHeight: '20px',
-};
-
-export const departmentTextStyle = {
-    color: colors['gray-7'],
-    fontSize: 12,
-    marginBottom: 12,
-};
-
-export const dividerStyle = {
-    margin: '16px 0 8px',
-};
-
-export const statsContainerStyle = css`
-    cursor: pointer;
-    color: ${colors['gray-7']};
-    font-size: 12px;
-    line-height: 18px;
-    transition: color 0.2s ease;
-
-    &:hover {
-        color: ${colors.primary};
-    }
-`;
-
-export const iconStyle = {
-    width: 14,
-    height: 14,
-};
-
-export const formatCount = (count: number): string => {
-    if (count >= 10000) {
-        return `${Math.floor(count / 10000)}w+`;
-    }
-    if (count >= 1000) {
-        return `${Math.floor(count / 1000)}k+`;
-    }
-    return count.toString();
-};
-
-export const actionButtonHoverStyle = css`
-    flex: 1;
-    background-color: #F2F2F2;
-    border-radius: 4px;
-    border: none;
-    padding: 0;
-    display: flex;
-    align-items: center;
-    justify-content: center;
-    transition: all 0.2s ease;
-
-    &:hover {
-        background-color: #E6E6E6 !important;
-        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
-        transform: translateY(-1px);
-    }
-`;
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/.gitignore	(date 1755246235709)
@@ -23,3 +23,35 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/workspace.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/cardBg.png
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42_\[Changes]/shelved.patch
+src/components/MCP/BaseMCPCard/index.tsx
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_16_03__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_16_03_\[Changes]/shelved.patch
Index: src/components/MCP/MCPServerCard/MCPServerList.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport {Flex, List} from 'antd';\nimport {CSSProperties, useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport {FilterValues, TabValues} from '../MCPServerFilter';\nimport {ALL_LABELS} from '../MCPServerFilter/LabelsFilterContent';\nimport {useLoadMore} from '../hooks';\nimport MCPServerCard from './MCPServerCard';\nimport {useFetchMCPServersContext} from './FetchMCPServersProvider';\n\nconst StyledList = styled(List<MCPServerBase>)`\n    .ant-list-grid .ant-col {\n        @media (min-width: 1600px) {\n            width: 25% !important;\n            max-width: 25% !important;\n            flex: 0 0 25% !important;\n        }\n    }\n`;\n\nconst PAGE_SIZE = 12;\n\nconst CARD_HEIGHT = 235;\n\nconst processEmptyText = (filterData?: FilterValues & TabValues) => {\n    if (filterData?.keywords?.trim()\n        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)\n        || filterData?.serverSourceType\n        || filterData?.serverProtocolType\n    ) {\n        return '暂无结果';\n    }\n    if (filterData?.favorite) {\n        return '暂无收藏的MCP Server';\n    }\n    if (filterData?.isMine) {\n        return '暂无发布的MCP Server';\n    }\n    return '暂无MCP Server';\n};\n\ninterface Props {\n    searchParams?: FilterValues & TabValues; // Define the type according to your needs\n    scrollableTarget: string;\n    style?: CSSProperties;\n}\nconst MCPServerList = ({searchParams, scrollableTarget, style = {}}: Props) => {\n    const {api: fetchServers} = useFetchMCPServersContext();\n    const [pageSize, setPageSize] = useState(PAGE_SIZE);\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return fetchServers(params);\n        },\n        [fetchServers]\n    );\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);\n\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    const emptyText = useMemo(\n        () => processEmptyText(searchParams),\n        [searchParams]\n    );\n\n    useLayoutEffect(\n        () => {\n            const bodyWidth = document.body.clientWidth;\n            const mediaCount = bodyWidth < 576 ? 2 : bodyWidth < 1600 ? 3 : 4;\n            const height = window.screen.height - 360;\n            const rows = Math.ceil(height / CARD_HEIGHT);\n            // 确保pagesizepageSize是mediaCounts的公倍数。\n            const minRows = rows % mediaCount + mediaCount;\n            const maxItems = minRows * mediaCount;\n            if (maxItems !== PAGE_SIZE) {\n                setPageSize(maxItems);\n            }\n        },\n        []\n    );\n\n    return (\n        <>\n            <InfiniteScroll\n                style={{...style, overflow: 'none', paddingBottom: '20px'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>上滑加载更多</div></Flex>}\n                scrollableTarget={scrollableTarget}\n            >\n                {(\n                    <StyledList\n                        grid={{\n                            gutter: 12,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 4,\n                        }}\n                        dataSource={list}\n                        rowKey=\"id\"\n                        renderItem={server => (\n                            <List.Item>\n                                <MCPServerCard refresh={refresh} key={server.id} server={server} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex justify=\"center\">\n                                            {emptyText}\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal />\n        </>\n    );\n};\n\nexport default MCPServerList;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPServerCard/MCPServerList.tsx b/src/components/MCP/MCPServerCard/MCPServerList.tsx
--- a/src/components/MCP/MCPServerCard/MCPServerList.tsx	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/src/components/MCP/MCPServerCard/MCPServerList.tsx	(date 1755250473673)
@@ -112,7 +112,7 @@
                         dataSource={list}
                         rowKey="id"
                         renderItem={server => (
-                            <List.Item>
+                            <List.Item style={{margin: 0, paddingBottom: 12}}>
                                 <MCPServerCard refresh={refresh} key={server.id} server={server} />
                             </List.Item>
                         )}
Index: src/icons/mcp/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { FC, SVGProps } from \"react\";\nimport { createIcon } from \"@panda-design/components\";\nimport IconInfoflow from \"./IconInfoflow\";\nimport IconOutlook from \"./IconOutlook\";\nimport SendActived from \"./SendActived\";\nimport AiTools1 from \"./AiTools1\";\nimport AiTools2 from \"./AiTools2\";\nimport AiTools3 from \"./AiTools3\";\nimport AiTools4 from \"./AiTools4\";\nimport Alert from \"./Alert\";\nimport ArrowRight from \"./ArrowRight\";\nimport CallCount from \"./CallCount\";\nimport Case from \"./Case\";\nimport Comment from \"./Comment\";\nimport Copy from \"./Copy\";\nimport Debug from \"./Debug\";\nimport Delete from \"./Delete\";\nimport Detail from \"./Detail\";\nimport Dev from \"./Dev\";\nimport DevBg from \"./DevBg\";\nimport DevBg1 from \"./DevBg1\";\nimport DevBg2 from \"./DevBg2\";\nimport Elipsis from \"./Elipsis\";\nimport ExitFullscreen from \"./ExitFullscreen\";\nimport Eye from \"./Eye\";\nimport Fullscreen from \"./Fullscreen\";\nimport Import from \"./Import\";\nimport LeftOutlined from \"./LeftOutlined\";\nimport LightMySpcae from \"./LightMySpcae\";\nimport LightPlayground from \"./LightPlayground\";\nimport List from \"./List\";\nimport Local from \"./Local\";\nimport LocalMcp from \"./LocalMcp\";\nimport Mcp from \"./Mcp\";\nimport McpAvatar from \"./McpAvatar\";\nimport MySpcae from \"./MySpcae\";\nimport OffcialExample from \"./OffcialExample\";\nimport Ops from \"./Ops\";\nimport OpsBg from \"./OpsBg\";\nimport OpsBg1 from \"./OpsBg1\";\nimport OpsBg2 from \"./OpsBg2\";\nimport Organization from \"./Organization\";\nimport Params from \"./Params\";\nimport Playground from \"./Playground\";\nimport PlaygroundConfig from \"./PlaygroundConfig\";\nimport Refresh from \"./Refresh\";\nimport Remote from \"./Remote\";\nimport RemoteMcp from \"./RemoteMcp\";\nimport Result from \"./Result\";\nimport RightArrow from \"./RightArrow\";\nimport Send from \"./Send\";\nimport Setting from \"./Setting\";\nimport ShowMore from \"./ShowMore\";\nimport SortAsc from \"./SortAsc\";\nimport SortDesc from \"./SortDesc\";\nimport Sse from \"./Sse\";\nimport Standard from \"./Standard\";\nimport Stdio from \"./Stdio\";\nimport StdioMcp from \"./StdioMcp\";\nimport Step01 from \"./Step01\";\nimport Step02 from \"./Step02\";\nimport Step03 from \"./Step03\";\nimport Step04 from \"./Step04\";\nimport StopGenerate from \"./StopGenerate\";\nimport Subscribe from \"./Subscribe\";\nimport Subscribe2 from \"./Subscribe2\";\nimport SubscribeFilled from \"./SubscribeFilled\";\nimport Subtract from \"./Subtract\";\nimport Tag from \"./Tag\";\nimport Test from \"./Test\";\nimport TestBg from \"./TestBg\";\nimport TestBg1 from \"./TestBg1\";\nimport TestBg2 from \"./TestBg2\";\nimport Tool from \"./Tool\";\nimport Unfold from \"./Unfold\";\nimport Vip from \"./Vip\";\n\nexport const IconIconInfoflow = createIcon(IconInfoflow);\nexport const IconIconOutlook = createIcon(IconOutlook);\nexport const IconSendActived = createIcon(SendActived);\nexport const IconAiTools1 = createIcon(AiTools1);\nexport const IconAiTools2 = createIcon(AiTools2);\nexport const IconAiTools3 = createIcon(AiTools3);\nexport const IconAiTools4 = createIcon(AiTools4);\nexport const IconAlert = createIcon(Alert);\nexport const IconArrowRight = createIcon(ArrowRight);\nexport const IconCallCount = createIcon(CallCount);\nexport const IconCase = createIcon(Case);\nexport const IconComment = createIcon(Comment);\nexport const IconCopy = createIcon(Copy);\nexport const IconDebug = createIcon(Debug);\nexport const IconDelete = createIcon(Delete);\nexport const IconDetail = createIcon(Detail);\nexport const IconDev = createIcon(Dev);\nexport const IconDevBg = createIcon(DevBg);\nexport const IconDevBg1 = createIcon(DevBg1);\nexport const IconDevBg2 = createIcon(DevBg2);\nexport const IconElipsis = createIcon(Elipsis);\nexport const IconExitFullscreen = createIcon(ExitFullscreen);\nexport const IconEye = createIcon(Eye);\nexport const IconFullscreen = createIcon(Fullscreen);\nexport const IconImport = createIcon(Import);\nexport const IconLeftOutlined = createIcon(LeftOutlined);\nexport const IconLightMySpcae = createIcon(LightMySpcae);\nexport const IconLightPlayground = createIcon(LightPlayground);\nexport const IconList = createIcon(List);\nexport const IconLocal = createIcon(Local);\nexport const IconLocalMcp = createIcon(LocalMcp);\nexport const IconMcp = createIcon(Mcp);\nexport const IconMcpAvatar = createIcon(McpAvatar);\nexport const IconMySpcae = createIcon(MySpcae);\nexport const IconOffcialExample = createIcon(OffcialExample);\nexport const IconOps = createIcon(Ops);\nexport const IconOpsBg = createIcon(OpsBg);\nexport const IconOpsBg1 = createIcon(OpsBg1);\nexport const IconOpsBg2 = createIcon(OpsBg2);\nexport const IconOrganization = createIcon(Organization);\nexport const IconParams = createIcon(Params);\nexport const IconPlayground = createIcon(Playground);\nexport const IconPlaygroundConfig = createIcon(PlaygroundConfig);\nexport const IconRefresh = createIcon(Refresh);\nexport const IconRemote = createIcon(Remote);\nexport const IconRemoteMcp = createIcon(RemoteMcp);\nexport const IconResult = createIcon(Result);\nexport const IconRightArrow = createIcon(RightArrow);\nexport const IconSend = createIcon(Send);\nexport const IconSetting = createIcon(Setting);\nexport const IconShowMore = createIcon(ShowMore);\nexport const IconSortAsc = createIcon(SortAsc);\nexport const IconSortDesc = createIcon(SortDesc);\nexport const IconSse = createIcon(Sse);\nexport const IconStandard = createIcon(Standard);\nexport const IconStdio = createIcon(Stdio);\nexport const IconStdioMcp = createIcon(StdioMcp);\nexport const IconStep01 = createIcon(Step01);\nexport const IconStep02 = createIcon(Step02);\nexport const IconStep03 = createIcon(Step03);\nexport const IconStep04 = createIcon(Step04);\nexport const IconStopGenerate = createIcon(StopGenerate);\nexport const IconSubscribe = createIcon(Subscribe);\nexport const IconSubscribe2 = createIcon(Subscribe2);\nexport const IconSubscribeFilled = createIcon(SubscribeFilled);\nexport const IconSubtract = createIcon(Subtract);\nexport const IconTag = createIcon(Tag);\nexport const IconTest = createIcon(Test);\nexport const IconTestBg = createIcon(TestBg);\nexport const IconTestBg1 = createIcon(TestBg1);\nexport const IconTestBg2 = createIcon(TestBg2);\nexport const IconTool = createIcon(Tool);\nexport const IconUnfold = createIcon(Unfold);\nexport const IconVip = createIcon(Vip);\n\nexport const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {\n    IconInfoflow: IconIconInfoflow,\n    IconOutlook: IconIconOutlook,\n    SendActived: IconSendActived,\n    aiTools1: IconAiTools1,\n    aiTools2: IconAiTools2,\n    aiTools3: IconAiTools3,\n    aiTools4: IconAiTools4,\n    alert: IconAlert,\n    arrowRight: IconArrowRight,\n    callCount: IconCallCount,\n    case: IconCase,\n    comment: IconComment,\n    copy: IconCopy,\n    debug: IconDebug,\n    delete: IconDelete,\n    detail: IconDetail,\n    dev: IconDev,\n    devBg: IconDevBg,\n    devBg1: IconDevBg1,\n    devBg2: IconDevBg2,\n    elipsis: IconElipsis,\n    exitFullscreen: IconExitFullscreen,\n    eye: IconEye,\n    fullscreen: IconFullscreen,\n    import: IconImport,\n    leftOutlined: IconLeftOutlined,\n    lightMySpcae: IconLightMySpcae,\n    lightPlayground: IconLightPlayground,\n    list: IconList,\n    local: IconLocal,\n    localMCP: IconLocalMcp,\n    mcp: IconMcp,\n    mcpAvatar: IconMcpAvatar,\n    mySpcae: IconMySpcae,\n    offcialExample: IconOffcialExample,\n    ops: IconOps,\n    opsBg: IconOpsBg,\n    opsBg1: IconOpsBg1,\n    opsBg2: IconOpsBg2,\n    organization: IconOrganization,\n    params: IconParams,\n    playground: IconPlayground,\n    playgroundConfig: IconPlaygroundConfig,\n    refresh: IconRefresh,\n    remote: IconRemote,\n    remoteMCP: IconRemoteMcp,\n    result: IconResult,\n    rightArrow: IconRightArrow,\n    send: IconSend,\n    setting: IconSetting,\n    showMore: IconShowMore,\n    sortAsc: IconSortAsc,\n    sortDesc: IconSortDesc,\n    sse: IconSse,\n    standard: IconStandard,\n    stdio: IconStdio,\n    stdioMCP: IconStdioMcp,\n    step01: IconStep01,\n    step02: IconStep02,\n    step03: IconStep03,\n    step04: IconStep04,\n    stopGenerate: IconStopGenerate,\n    subscribe: IconSubscribe,\n    subscribe2: IconSubscribe2,\n    subscribeFilled: IconSubscribeFilled,\n    subtract: IconSubtract,\n    tag: IconTag,\n    test: IconTest,\n    testBg: IconTestBg,\n    testBg1: IconTestBg1,\n    testBg2: IconTestBg2,\n    tool: IconTool,\n    unfold: IconUnfold,\n    vip: IconVip,\n};\n\nexport default iconsMap;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/index.ts b/src/icons/mcp/index.ts
--- a/src/icons/mcp/index.ts	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/src/icons/mcp/index.ts	(date 1755246151595)
@@ -9,6 +9,7 @@
 import AiTools4 from "./AiTools4";
 import Alert from "./Alert";
 import ArrowRight from "./ArrowRight";
+import ArrowRight1 from "./ArrowRight1";
 import CallCount from "./CallCount";
 import Case from "./Case";
 import Comment from "./Comment";
@@ -84,6 +85,7 @@
 export const IconAiTools4 = createIcon(AiTools4);
 export const IconAlert = createIcon(Alert);
 export const IconArrowRight = createIcon(ArrowRight);
+export const IconArrowRight1 = createIcon(ArrowRight1);
 export const IconCallCount = createIcon(CallCount);
 export const IconCase = createIcon(Case);
 export const IconComment = createIcon(Comment);
@@ -160,6 +162,7 @@
     aiTools4: IconAiTools4,
     alert: IconAlert,
     arrowRight: IconArrowRight,
+    arrowRight1: IconArrowRight1,
     callCount: IconCallCount,
     case: IconCase,
     comment: IconComment,
Index: src/components/MCP/BaseMCPCard/styles.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {css} from '@emotion/css';\nimport {colors} from '@/constants/colors';\n\nexport const containerCss = css`\n    padding: 17px 24px 12px;\n    position: relative;\n    transition: all 0.3s ease;\n    &:hover {\n        position: relative;\n        z-index: 1;\n        background: ${colors.white};\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n        border-radius: 6px;\n        padding-bottom: 48px;\n        margin-bottom: -48px;\n        width: auto;\n        .hover-actions {\n            opacity: 1;\n            min-height: 32px;\n            padding: 0 20px 16px;\n        }\n    }\n`;\n\nexport const hoverActionsStyle = css`\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    max-height: 0;\n    opacity: 0;\n    transition: all 0.3s ease;\n`;\n\nexport const DescriptionContainer = styled.div`\n    margin: 20px 0 12px;\n    font-size: 14px;\n    line-height: 22px;\n    position: relative;\n    height: 44px;\n    overflow: hidden;\n`;\n\nexport const DescriptionText = styled.div`\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    word-break: break-word;\n    overflow: hidden;\n`;\n\nexport const EllipsisOverlay = styled.div`\n    position: absolute;\n    bottom: 9px;\n    right: 12px;\n    padding-left: 10px;\n    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);\n    pointer-events: none;\n`;\n\nexport const cardContentStyle = {\n    overflow: 'hidden',\n    flex: 1,\n};\n\nexport const protocolTextStyle = {\n    color: colors['gray-7'],\n    fontSize: 12,\n    lineHeight: '20px',\n};\n\nexport const dividerStyle = {\n    margin: '16px 0 8px',\n};\n\nexport const statsContainerStyle = css`\n    color: ${colors['gray-7']};\n    font-size: 12px;\n    line-height: 20px;\n    transition: color 0.2s ease;\n    &:hover {\n        color: ${colors.primary};\n    }\n`;\n\nexport const iconStyle = {\n    width: 14,\n    height: 14,\n};\n\nexport const actionButtonStyle = {\n    fontSize: '12px',\n    lineHeight: '20px',\n    padding: 0,\n    height: 20,\n    gap: 4,\n    color: '#545454',\n};\n\nexport const fullWidthButtonStyle = {\n    width: '100%',\n};\n\nexport const formatCount = (count: number): string => {\n    if (count >= 10000) {\n        return `${Math.floor(count / 10000)}w+`;\n    }\n    if (count >= 1000) {\n        return `${Math.floor(count / 1000)}k+`;\n    }\n    return count.toString();\n};\n\nexport const actionButtonHoverStyle = css`\n    flex: 1;\n    border-radius: 4px;\n    border: 1px solid #BFBFBF;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.2s ease;\n\n    &:hover {\n        background-color: #E6E6E6 !important;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        transform: translateY(-1px);\n    }\n`;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/styles.ts b/src/components/MCP/BaseMCPCard/styles.ts
--- a/src/components/MCP/BaseMCPCard/styles.ts	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/src/components/MCP/BaseMCPCard/styles.ts	(date 1755251068407)
@@ -1,24 +1,27 @@
 import styled from '@emotion/styled';
 import {css} from '@emotion/css';
 import {colors} from '@/constants/colors';
+import bg from '@/assets/mcp/cardBg.png';
+import vipbg from '@/assets/mcp/cardVipBg.png';
 
-export const containerCss = css`
+export const containerCss = ({official}: {official?: boolean}) => css`
     padding: 17px 24px 12px;
     position: relative;
-    transition: all 0.3s ease;
+    height: 213px;
+    border-radius: 6px;
     &:hover {
-        position: relative;
-        z-index: 1;
-        background: ${colors.white};
-        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
-        border-radius: 6px;
-        padding-bottom: 48px;
-        margin-bottom: -48px;
-        width: auto;
+        position: absolute;
+        top: 0;
+        left: 0;
+        right: 0;
+        z-index: 2;
+        background: url(${official ? vipbg : bg}) no-repeat;
+        background-size: cover;
+        height: 277px;
         .hover-actions {
             opacity: 1;
-            min-height: 32px;
-            padding: 0 20px 16px;
+            min-height: 51px;
+            padding: 0 24px 20px;
         }
     }
 `;
@@ -34,11 +37,12 @@
 `;
 
 export const DescriptionContainer = styled.div`
-    margin: 20px 0 12px;
+    margin: 17px 0 12px;
     font-size: 14px;
     line-height: 22px;
     position: relative;
     height: 44px;
+    color: #545454;
     overflow: hidden;
 `;
 
@@ -66,9 +70,9 @@
 };
 
 export const protocolTextStyle = {
-    color: colors['gray-7'],
+    color: '#8F8F8F',
     fontSize: 12,
-    lineHeight: '20px',
+    lineHeight: '18px',
 };
 
 export const dividerStyle = {
@@ -100,7 +104,11 @@
 };
 
 export const fullWidthButtonStyle = {
+    background: 'var(--Tokens-, #F2F2F2)',
+    border: 'none',
     width: '100%',
+    height: '32px',
+    gap: 4,
 };
 
 export const formatCount = (count: number): string => {
Index: src/components/MCP/TagGroup.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, Tooltip} from 'antd';\nimport {Tag, TagColor} from '@panda-design/components';\nimport {CSSProperties, ReactNode} from 'react';\nimport {IconTag} from '@/icons/mcp';\n\ninterface Label {\n    id: number;\n    label: string;\n}\nconst TooltipTitleWrapper = styled.div`\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    padding: 4px 0;\n`;\n\nconst COLOR_MAP: Record<string, CSSProperties> = {\n    'light-purple': {\n        backgroundColor: '#EBE6F9',\n        color: '#8264DE',\n    },\n    'gray': {\n        backgroundColor: '#E2E8F0',\n        color: '#4B6C9F',\n    },\n};\n\ninterface Props {\n    prefix?: ReactNode;\n    color?: TagColor;\n    labels: Label[];\n    maxNum?: number;\n    style?: CSSProperties;\n    gap?: number;\n}\n\nconst TagGroup = ({prefix = <IconTag />, labels, maxNum = 2, color = 'info', style, gap = 8}: Props) => {\n    const colorStyle = COLOR_MAP[color];\n    const innerColor = COLOR_MAP[color] ? undefined : color;\n    return (\n        <Flex style={{zIndex: 1, gap, height: 22, ...style}} align=\"center\">\n            {prefix}\n            {\n                labels.length > 0 ? labels.slice(0, maxNum).map(label => {\n                    return (\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            key={label.id}\n                            style={{\n                                margin: 0,\n                                flexShrink: 1,\n                                overflow: 'hidden',\n                                textOverflow: 'ellipsis',\n                                ...colorStyle,\n                            }}\n                        >\n                            {label.label}\n                        </Tag>\n                    );\n                }) : (\n                    <Tag\n                        type=\"flat\"\n                        color={innerColor}\n                        style={{\n                            margin: 0,\n                            opacity: 0,\n                            ...colorStyle,\n                        }}\n                    >\n                        占位标签\n                    </Tag>\n                )\n            }\n            {\n                labels.length > maxNum && (\n                    <Tooltip\n                        title={\n                            <TooltipTitleWrapper>\n                                {labels.slice(maxNum).map(label => {\n                                    return (\n                                        <Tag\n                                            type=\"flat\"\n                                            color={innerColor}\n                                            key={label.id}\n                                            style={{\n                                                margin: 0,\n                                                ...colorStyle,\n                                            }}\n                                        >\n                                            {label.label}\n                                        </Tag>\n                                    );\n                                })}\n                            </TooltipTitleWrapper>\n                        }\n                    >\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            style={{\n                                margin: 0,\n                                ...colorStyle,\n                            }}\n                        >\n                            +{labels.length - maxNum}\n                        </Tag>\n                    </Tooltip>\n                )\n            }\n        </Flex>\n    );\n};\n\nexport default TagGroup;\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/TagGroup.tsx b/src/components/MCP/TagGroup.tsx
--- a/src/components/MCP/TagGroup.tsx	(revision 6f53231a24e2e41335863723a9ce64bcce03827e)
+++ b/src/components/MCP/TagGroup.tsx	(date 1755246381127)
@@ -35,7 +35,7 @@
     gap?: number;
 }
 
-const TagGroup = ({prefix = <IconTag />, labels, maxNum = 2, color = 'info', style, gap = 8}: Props) => {
+const TagGroup = ({prefix = <IconTag />, labels, maxNum = 4, color = 'info', style, gap = 8}: Props) => {
     const colorStyle = COLOR_MAP[color];
     const innerColor = COLOR_MAP[color] ? undefined : color;
     return (
