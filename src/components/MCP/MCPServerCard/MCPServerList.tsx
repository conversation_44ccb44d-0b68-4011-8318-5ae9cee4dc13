/* eslint-disable max-lines */
import {Flex, List} from 'antd';
import {CSSProperties, useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from '@emotion/styled';
import {MCPServerBase} from '@/types/mcp/mcp';
import CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';
import MCPEmpty from '@/design/MCP/MCPEmpty';
import {FilterValues, TabValues} from '../MCPServerFilter';
import {ALL_LABELS} from '../MCPServerFilter/LabelsFilterContent';
import {useLoadMore} from '../hooks';
import MCPServerCard from './MCPServerCard';
import {useFetchMCPServersContext} from './FetchMCPServersProvider';

const StyledList = styled(List<MCPServerBase>)`
    .ant-list-grid .ant-col {
        @media (min-width: 1600px) {
            width: 25% !important;
            max-width: 25% !important;
            flex: 0 0 25% !important;
        }
    }
`;

const PAGE_SIZE = 12;

const CARD_HEIGHT = 235;

const processEmptyText = (filterData?: FilterValues & TabValues) => {
    if (filterData?.keywords?.trim()
        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)
        || filterData?.serverSourceType
        || filterData?.serverProtocolType
    ) {
        return '暂无结果';
    }
    if (filterData?.favorite) {
        return '暂无收藏的MCP Server';
    }
    if (filterData?.isMine) {
        return '暂无发布的MCP Server';
    }
    return '暂无MCP Server';
};

interface Props {
    searchParams?: FilterValues & TabValues; // Define the type according to your needs
    scrollableTarget: string;
    style?: CSSProperties;
}
const MCPServerList = ({searchParams, scrollableTarget, style = {}}: Props) => {
    const {api: fetchServers} = useFetchMCPServersContext();
    const [pageSize, setPageSize] = useState(PAGE_SIZE);
    const api = useCallback(
        (params: {current: number, limit: number}) => {
            return fetchServers(params);
        },
        [fetchServers]
    );
    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);

    useEffect(
        () => {
            refresh();
        },
        [refresh]
    );

    const emptyText = useMemo(
        () => processEmptyText(searchParams),
        [searchParams]
    );

    useLayoutEffect(
        () => {
            const bodyWidth = document.body.clientWidth;
            const mediaCount = bodyWidth < 576 ? 2 : bodyWidth < 1600 ? 3 : 4;
            const height = window.screen.height - 360;
            const rows = Math.ceil(height / CARD_HEIGHT);
            // 确保pagesizepageSize是mediaCounts的公倍数。
            const minRows = rows % mediaCount + mediaCount;
            const maxItems = minRows * mediaCount;
            if (maxItems !== PAGE_SIZE) {
                setPageSize(maxItems);
            }
        },
        []
    );

    return (
        <>
            <InfiniteScroll
                style={{...style, overflow: 'none', paddingBottom: '20px'}}
                dataLength={list.length || 0}
                next={loadMore}
                hasMore={total > list.length}
                loader={<Flex justify="center" align="center"><div>上滑加载更多</div></Flex>}
                scrollableTarget={scrollableTarget}
                scrollThreshold="20px"
            >
                {(
                    <StyledList
                        grid={{
                            gutter: 12,
                            column: 2,
                            xs: 2,
                            sm: 3,
                            md: 3,
                            lg: 3,
                            xl: 3,
                            xxl: 4,
                        }}
                        dataSource={list}
                        rowKey="id"
                        renderItem={server => (
                            <List.Item style={{margin: 0, paddingBottom: 12}}>
                                <MCPServerCard refresh={refresh} key={server.id} server={server} />
                            </List.Item>
                        )}
                        locale={{
                            emptyText: (
                                <MCPEmpty
                                    description={(
                                        <Flex justify="center">
                                            {emptyText}
                                        </Flex>
                                    )}
                                />
                            ),
                        }}
                    />
                )}
            </InfiniteScroll>
            <CreateMCPAppModal />
        </>
    );
};

export default MCPServerList;
