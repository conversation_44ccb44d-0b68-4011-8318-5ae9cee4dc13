/* eslint-disable max-lines */
import {Divider, Flex, Typography} from 'antd';
import {SettingOutlined} from '@ant-design/icons';
import {ReactNode, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {css} from '@emotion/css';
import iconsMap from '@/icons/mcp';
import {MCPZoneDetail} from '@/api/mcp';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
// import {useFetchMCPServersContext} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import SvgDevelopZoneLeftBg from '@/assets/mcp/DevelopZoneLeftBg';
import SvgDevelopZoneRightBg from '@/assets/mcp/DevelopZoneRightBg';
import SvgOperationZoneLeftBg from '@/assets/mcp/OperationZoneLeftBg';
import SvgOperationZoneRightBg from '@/assets/mcp/OperationZoneRightBg';
import OperationZoneBg from '@/assets/mcp/operationZoneBg.jpg';
import DevelopZoneBg from '@/assets/mcp/developZoneBg.jpg';
import {ZoneHeaderBreadcrumb} from './ZoneHeaderBreadcrumb';
import {DEVELOP_ZONE_ID} from './constant';
// import {ZoneTabs} from './ZoneTabs';

interface Props {
  zone?: MCPZoneDetail;
}

interface PropertyProps{
  name: string;
  value: ReactNode;
}

const HeaderProperty = ({name, value}: PropertyProps) => {
    return (
        <Flex>
            <Typography.Text style={{color: '#5C5C5C'}}>{name}</Typography.Text>
            <div style={{marginLeft: '24px'}}>
                {value}
            </div>
        </Flex>
    );
};

interface StatisticsProps {
  label: string;
  icon: ReactNode;
  number: string | number;
  splitLine?: boolean;
}

const ZoneStatistics = ({label, icon, number, splitLine}: StatisticsProps) => {
    return (
        <Flex align="center">
            <Flex>
                <span>{icon}</span>
                <span style={{color: '#5C5C5C', marginLeft: '4px'}}>{label}</span>
            </Flex>
            <span style={{marginLeft: '8px', fontSize: '14px', fontWeight: 400}}>{number}</span>
            {
                splitLine && <Divider type="vertical" style={{margin: '0 32px', borderColor: '#CCCCCB'}} />
            }
        </Flex>
    );
};

const extractZoneBaseProperties = (zone: MCPZoneDetail) => {
    return {
        'id': zone.id,
        'name': zone.name,
        'type': zone.type,
        'description': zone.description,
        'announcement': zone.announcement,
        'publisher': zone.publisher,
        'department': zone.department,
        'icon': zone.icon,
        'serverCount': zone.serverCount,
        'subCount': zone.subCount,
        'commentCount': zone.commentCount,
    };
};

type FormatedZone = MCPZoneDetail | {name: string, childZones: MCPZoneDetail[]} | null;

const useZoneProperties = (zone?: MCPZoneDetail): FormatedZone => {
    const [formated, setFormated] = useState(null);
    useEffect(
        () => {
            if (zone) {
                if (zone.childZones.length > 0) {
                    setFormated(() => {
                        const childZones = [{
                            ...extractZoneBaseProperties(zone),
                            name: '全部',

                        }];
                        zone.childZones.forEach(child => {
                            childZones.push(extractZoneBaseProperties(child));
                        });
                        return {
                            name: zone.name,
                            childZones,
                        };
                    });
                } else {
                    setFormated({
                        ...extractZoneBaseProperties(zone),
                        childZones: [],
                    });
                }
            }
        },
        [zone]
    );
    return formated;
};

const operationBg = 'linear-gradient(22.53deg, rgba(238, 238, 255, 0.24) -114.77%, rgba(241, 244, 251, 0.8) 77.18%)';

const developBg = 'linear-gradient(357.82deg, rgba(238, 249, 255, 0.4) -63.55%, rgba(240, 249, 255, 0.8) 117.64%)';

const Container = styled(Flex)<{zoneId: string}>`
    position: relative;
    padding: 24px;
    background: ${props => (props.zoneId === DEVELOP_ZONE_ID ? developBg : operationBg)};
`;

const LeftBgContainer = ({zoneId}: {zoneId?: string}) => {
    return (
        <div style={{
            position: 'absolute',
            left: 0,
            top: 0,
            zIndex: 0,
        }}
        >
            {zoneId === DEVELOP_ZONE_ID ? <SvgDevelopZoneLeftBg /> : <SvgOperationZoneLeftBg />}
        </div>
    );
};

const RightBgContainer = ({zoneId}: {zoneId?: string}) => {
    return (
        <div style={{
            position: 'absolute',
            right: 0,
            top: 0,
            zIndex: 0,
        }}
        >
            {zoneId === DEVELOP_ZONE_ID ? <SvgDevelopZoneRightBg /> : <SvgOperationZoneRightBg />}
        </div>
    );
};

const css1 = css`
    .ant-5-skeleton{
        width: 100% !important;
        height: 100% !important;
    }
    .ant-5-skeleton-image{
        width: 100% !important;
        height: 100% !important;
    }
`;

const MainBgContainer = ({zoneId}: {zoneId?: string}) => {
    return (
        <div
            style={{
                position: 'relative',
                borderRadius: '4px',
                height: '185px',
                overflow: 'hidden',
                flex: 3,
                marginRight: '30px',
                maxWidth: '1000px',
            }}
            className={css1}
        >
            <img
                style={{position: 'absolute', left: '0', top: '-190px', height: '560px'}}
                src={zoneId === DEVELOP_ZONE_ID ? DevelopZoneBg : OperationZoneBg}
            />
        </div>
    );
};

export const ZoneHeader = ({zone}: Props) => {
    // const {changeSearchParams} = useFetchMCPServersContext();
    const [currentZone, setCurrentZone] = useState<MCPZoneDetail|null>(null);
    const formatedZone = useZoneProperties(zone);
    useEffect(
        () => {
            if (formatedZone) {
                setCurrentZone(
                    formatedZone.childZones.length > 0
                        ? formatedZone.childZones[0] : formatedZone as MCPZoneDetail);
            }
        },
        [formatedZone]
    );
    // const changeZone = useCallback(
    //     (zone: MCPZoneDetail) => {
    //         setCurrentZone(zone);
    //         changeSearchParams({zoneId: zone.id});
    //     },
    //     [changeSearchParams]

    // );
    return (
        <Container vertical gap="20px" zoneId={zone?.id}>
            <LeftBgContainer zoneId={zone?.id} />
            <RightBgContainer zoneId={zone?.id} />
            <Flex justify="space-between" style={{zIndex: 1}}>
                <ZoneHeaderBreadcrumb>{formatedZone?.name}</ZoneHeaderBreadcrumb>
                {/* 管理占位 */}
                <Button style={{display: 'none'}} color="default" variant="link" icon={<SettingOutlined />} href="">
                    管理
                </Button>
            </Flex>
            {/* 还未设计，先不考虑 */}
            {/* {
                formatedZone?.childZones && formatedZone.childZones.length > 0
                    && <ZoneTabs zones={formatedZone.childZones} setZoneContent={changeZone} />
            } */}
            <Flex>
                <MainBgContainer zoneId={zone?.id} />
                <Flex vertical gap="16px" style={{flexBasis: '550px', marginLeft: '20px', flexGrow: 1, flexShrink: 0}}>
                    <Typography.Text
                        style={{fontSize: '16px', lineHeight: '24px'}}
                    >
                        {currentZone?.description}
                    </Typography.Text>
                    <Divider style={{margin: '0'}} />
                    <HeaderProperty name="出品人" value={currentZone?.department} />
                    <HeaderProperty name="联系人" value={<UserAvatarList users={currentZone?.publisher} max={2} />} />
                    <Flex>
                        <ZoneStatistics
                            label="MCP数"
                            icon={<iconsMap.mcp />}
                            number={currentZone?.serverCount}
                            splitLine
                        />
                        <ZoneStatistics
                            label="订阅量"
                            icon={<iconsMap.subscribe2 />}
                            number={currentZone?.subCount}
                        />
                        {/* <ZoneStatistics
                            label="评论数"
                            icon={<iconsMap.comment />}
                            number={currentZone?.commentCount}
                        /> */}
                    </Flex>
                </Flex>
            </Flex>
        </Container>
    );
};
