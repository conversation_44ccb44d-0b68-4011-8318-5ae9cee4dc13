import {useCallback, useEffect, useMemo, useState} from 'react';
import {Flex, List, Space} from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from '@emotion/styled';
import {apiGetMCPServerListByWorkspace} from '@/api/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPServerBase, MCPServerStatus} from '@/types/mcp/mcp';
import {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';
import {Gap} from '@/design/iplayground/Gap';
import CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';
import MCPEmpty from '@/design/MCP/MCPEmpty';
import SpaceMCPCard from './SpaceMCPCard';
import {useLoadMore} from './hooks';

const PAGE_SIZE = 18;

const Container = styled.div`
    flex: 1;
    overflow-y: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const StyledLabel = styled.span`
    display: inline-flex;
    gap: 16px;
    align-items: center;
    color: #8F8F8F;
    &: after{
        content: '';
        width: 1px;
        height: 16px;
        background: #D9D9D9;
    }
`;

const StyledList = styled(List<MCPServerBase>)`
    .ant-list-grid .ant-col {
        @media (min-width: 1600px) {
            width: 25% !important;
            max-width: 25% !important;
            flex: 0 0 25% !important;
        }
    }
`;

const MCPListPanel = () => {
    const workspaceId = useMCPWorkspaceId();
    const [selectedStatus, setSelectedStatus] = useState<MCPServerStatus | 'all'>('all');

    const api = useCallback(
        (params: {current: number, limit: number}) => {
            return apiGetMCPServerListByWorkspace({
                workspaceId,
                size: params.limit,
                pn: params.current,
                status: selectedStatus !== 'all' ? selectedStatus : undefined,
            });
        },
        [workspaceId, selectedStatus]
    );

    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, PAGE_SIZE);

    // TODO：接口上线后移除
    const dataSource = useMemo(
        () => {
            if (selectedStatus === 'all') {
                return list;
            }
            return list?.filter(item => item.serverStatus === selectedStatus);
        },
        [list, selectedStatus]
    );
    // const [newFirst, setNewFirst] = useState(true);
    useEffect(
        () => {
            refresh();
        },
        [refresh]
    );

    return (
        <Container id="scrollableDiv">
            <Space direction="vertical">
                <Space>
                    <StyledLabel>MCP状态</StyledLabel>
                    <RadioButtonGroup
                        value={selectedStatus}
                        onChange={setSelectedStatus}
                        options={[
                            {label: '全部', value: 'all'},
                            {label: '已发布', value: 'release'},
                            {label: '草稿', value: 'draft'},
                        ]}
                    />
                </Space>
                {/* <Space>
                    <span>发布时间</span>
                    <Button
                        onClick={() => setNewFirst(value => !value)}
                        type="text"
                    >
                        {newFirst ? '新到旧' : '旧到新'} <StyledIcon newFirst={newFirst} />
                    </Button>
                </Space> */}
            </Space>
            <Gap />
            <InfiniteScroll
                // 挂一个key，避免切换type的时候出问题
                key={selectedStatus}
                style={{overflow: 'none'}}
                dataLength={list.length || 0}
                next={loadMore}
                hasMore={total > list.length}
                loader={<Flex justify="center" align="center"><div>上滑加载更多</div></Flex>}
                // endMessage={<Divider plain>到底了</Divider>}
                scrollableTarget="scrollableDiv"
            >
                {(
                    <StyledList
                        dataSource={dataSource}
                        grid={{
                            gutter: 12,
                            column: 2,
                            xs: 2,
                            sm: 3,
                            md: 3,
                            lg: 3,
                            xl: 3,
                            xxl: 4,
                        }}
                        rowKey="id"
                        renderItem={item => (
                            <List.Item key={item.id} style={{margin: 0, paddingBottom: 12}}>
                                <SpaceMCPCard refresh={refresh} server={item} />
                            </List.Item>
                        )}
                        locale={{
                            emptyText: (
                                <MCPEmpty
                                    description={(
                                        <Flex align="center" justify="center">
                                            <span>暂无MCP Server</span>
                                        </Flex>
                                    )}
                                />
                            ),
                        }}
                    />
                )}
            </InfiniteScroll>
            <CreateMCPAppModal onSuccess={refresh} />
        </Container>
    );
};

export default MCPListPanel;
