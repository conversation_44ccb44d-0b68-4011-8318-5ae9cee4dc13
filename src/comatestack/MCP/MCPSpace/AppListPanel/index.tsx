import {Flex, List, Select, Space, Typography} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useEffect, useMemo, useState} from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPApplication} from '@/types/mcp/application';
import {apiGetWorkspaceApplications} from '@/api/mcp';
import {CreateMCPAppButton} from '@/components/MCP/CreateMCPAppButton';
import MCPEmpty from '@/design/MCP/MCPEmpty';
import {loadWorkspaceServerList, useWorkspaceServerList} from '@/regions/mcp/mcpSpace';
import {useLoadMore} from '../MCPListPanel/hooks';
import AppCard from './AppCard';

const Container = styled.div`
    flex: 1;
    overflow-y: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const PAGE_SIZE = 18;
const VALUE_FOR_ALL = -1;

interface Props {
    searchTxt?: string;
}
const AppListPanel = ({searchTxt}: Props) => {
    const validSearchTxt = searchTxt?.trim();
    const [serverId, setServerId] = useState<number>(VALUE_FOR_ALL);

    const workspaceId = useMCPWorkspaceId();
    const api = useCallback(
        (params: {current: number, limit: number}) => {
            return apiGetWorkspaceApplications({
                workspaceId,
                size: params.limit,
                pn: params.current,
                keywords: validSearchTxt,
                serverId: serverId === VALUE_FOR_ALL ? undefined : serverId,
            });
        },
        [workspaceId, validSearchTxt, serverId]
    );

    const {loadMore, total, list, refresh, loading} = useLoadMore<MCPApplication>(api, PAGE_SIZE);

    useEffect(
        () => {
            refresh();
        },
        [refresh]
    );

    const servers = useWorkspaceServerList(workspaceId);
    const serverOptions = useMemo(
        () => [
            {value: VALUE_FOR_ALL, label: '全部'},
            ...(servers ?? []).map(server => ({value: server.id, label: server.name})),
        ],
        [servers]
    );
    useEffect(
        () => {
            loadWorkspaceServerList({workspaceId, pn: 1, size: 100});
        },
        [workspaceId]
    );

    return (
        <Flex vertical>
            <Space>
                订阅此MCP的应用
                <Select
                    style={{width: 240}}
                    options={serverOptions}
                    value={serverId}
                    onChange={setServerId}
                />
            </Space>
            <Typography.Text type="secondary" style={{marginTop: '24px', marginBottom: '12px'}}>
                应用是您在平台上管理MCP订阅的单元，直接对应可使用的产品（如一个助手）。创建应用后可以获得MCP网关的访问授权，同时可以自主选择MCP Server中的工具，使模型识别更精准。
            </Typography.Text>
            <Container id="scrollableDiv">
                <InfiniteScroll
                    style={{overflow: 'none'}}
                    height="100%"
                    dataLength={list.length || 0}
                    next={loadMore}
                    hasMore={total > list.length}
                    loader={<Flex justify="center" align="center"><div>上滑加载更多</div></Flex>}
                    scrollableTarget="scrollableDiv"
                >
                    {(
                        <List
                            dataSource={list}
                            grid={{
                                gutter: 12,
                                column: 2,
                                xs: 2,
                                sm: 3,
                                md: 3,
                                lg: 3,
                                xl: 3,
                                xxl: 4,
                            }}
                            loading={loading}
                            rowKey="id"
                            renderItem={item => (
                                <List.Item key={item.id}>
                                    <AppCard item={item} />
                                </List.Item>
                            )}
                            locale={{
                                emptyText: (
                                    <MCPEmpty
                                        description={(
                                            <Flex align="center" justify="center">
                                                <span>暂无应用，</span>
                                                <CreateMCPAppButton type="link" />
                                            </Flex>
                                        )}
                                    />
                                ),
                            }}
                        />
                    )}
                </InfiniteScroll>
                <CreateMCPAppModal workspaceId={workspaceId} />
            </Container>
        </Flex>
    );
};

export default AppListPanel;
