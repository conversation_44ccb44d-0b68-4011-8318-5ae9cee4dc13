import {Avatar as AntAvatar, AvatarProps, Tooltip} from 'antd';
import Highlight from 'react-highlight-words';
import {css, cx} from '@emotion/css';
import {marginLeft, ellipsis} from '@panda-design/components';
import {IconEmailGroup} from '@/icons/lucide';
import {IconPassport, IconUserGroup} from '@/icons/comatestack';
import {useUserBaseInfo} from '../MCP/useUserBaseInfo';

const containerCss = css`
    display: inline-flex;
    align-items: center;
    text-wrap: nowrap;
`;

const getAvatarProps = (username: string, userType?: string): Partial<AvatarProps> => {
    if (username.includes('@')) {
        return {icon: <IconEmailGroup />};
    }
    if (userType === 'USER_GROUP') {
        return {icon: <IconUserGroup fontSize={22} />};
    }
    if (userType === 'PASSPORT') {
        return {icon: <IconPassport fontSize={22} />};
    }
    return {src: `https://eefe.baidu-int.com/avatars/${username}`};
};

interface Props {
    username: string;
    showText?: boolean;
    iconSize?: 'small' | 'default' | 'large' | number;
    keyword?: string;
    className?: string;
    imageWidth?: number;
    showTooltipUsername?: boolean;
    userType?: string;
    showChineseName?: boolean;
}

const UserAvatar = ({
    username,
    showText = false,
    iconSize = 'small',
    keyword,
    className,
    showTooltipUsername = false,
    userType,
    showChineseName,
}: Props) => {
    const userInfo = useUserBaseInfo(username);
    if (!username) {
        return null;
    }

    const avatarProps = getAvatarProps(username, userType);
    const name = showChineseName && userInfo?.chineseName ? userInfo?.chineseName : username;
    return (
        <div className={cx(containerCss, ellipsis, className)}>
            <Tooltip title={showTooltipUsername ? name : ''}>
                <AntAvatar {...avatarProps} size={iconSize} />
            </Tooltip>
            {showText && (
                <span className={marginLeft(4)}>
                    <Highlight
                        // 这俩是开启小k能力的属性，不要改
                        data-k-type="user"
                        data-k-value={username}
                        searchWords={[keyword]}
                        textToHighlight={name}
                    />
                </span>
            )}
        </div>
    );
};

export default UserAvatar;
